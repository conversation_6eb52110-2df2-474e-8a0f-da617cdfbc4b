#!/usr/bin/env python3
"""
Test script for LinkedIn Post Scraper
"""

from linkedin_scraper import LinkedInPostScraper
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_scraper():
    """Test the scraper with a few URLs"""
    scraper = LinkedInPostScraper()
    
    try:
        # Read test URLs
        with open('test_urls.txt', 'r') as f:
            urls = [line.strip() for line in f if line.strip()]
        
        logger.info(f"Testing with {len(urls)} URLs")
        
        # Test individual components first
        print("Testing URL parsing...")
        for url in urls:
            post_id = scraper.extract_post_id(url)
            print(f"URL: {url}")
            print(f"Post ID: {post_id}")
            print()
        
        # Test timestamp extraction for one URL
        print("Testing timestamp extraction...")
        test_url = urls[0]
        timestamp_data = scraper.get_timestamp_from_api(test_url)
        print(f"Timestamp data for {test_url}:")
        print(f"Uploaded on: {timestamp_data.get('uploaded_on', 'N/A')}")
        print(f"UTC: {timestamp_data.get('utc', 'N/A')}")
        print()
        
        # Test full scraping for one URL
        print("Testing full post scraping...")
        post_data = scraper.scrape_post(test_url)
        print("Post data:")
        for key, value in post_data.items():
            print(f"{key}: {value}")
        
    except Exception as e:
        logger.error(f"Error in test: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    test_scraper()
