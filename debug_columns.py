import pandas as pd

# Read the new file and check its structure
print("Reading linkedin_post_data.csv...")
new_df = pd.read_csv('linkedin_post_data.csv')

print("Original shape:", new_df.shape)
print("\nOriginal columns:")
for i, col in enumerate(new_df.columns):
    print(f"{i}: '{col}'")

# Fix the headers
if 'Engagement metrics for individual posts' in str(new_df.iloc[0, 0]):
    # Get the actual column names from the first row
    new_columns = new_df.iloc[0].tolist()
    new_df.columns = new_columns
    # Skip the first row (which is now our header)
    new_df = new_df.iloc[1:].reset_index(drop=True)

print(f"\nAfter fixing headers:")
print("Shape:", new_df.shape)
print("\nColumns:")
for i, col in enumerate(new_df.columns):
    print(f"{i}: '{col}'")

print("\nFirst few rows:")
print(new_df.head(2))
