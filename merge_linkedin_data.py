import pandas as pd
import re
from urllib.parse import urlparse, parse_qs

def extract_post_id_from_url(url):
    """Extract post ID from LinkedIn URL"""
    if pd.isna(url) or not url:
        return None
    
    # Try to extract from activity ID in URL
    activity_match = re.search(r'activity-(\d+)', url)
    if activity_match:
        return activity_match.group(1)
    
    # Try to extract from urn:li:activity format
    urn_match = re.search(r'urn:li:activity:(\d+)', url)
    if urn_match:
        return urn_match.group(1)
    
    return None

def clean_text(text):
    """Clean and normalize text content"""
    if pd.isna(text):
        return ""
    
    # Remove extra whitespace and normalize line breaks
    text = re.sub(r'\s+', ' ', str(text).strip())
    return text

def merge_linkedin_files():
    """Merge the two LinkedIn CSV files"""
    
    # Read both files
    print("Reading original linkedin_posts.csv...")
    original_df = pd.read_csv('linkedin_posts.csv')
    
    print("Reading new linkedin_post_data.csv...")
    # Read the CSV and skip the first row which is a description
    new_df = pd.read_csv('linkedin_post_data.csv', skiprows=1)

    # The actual column names are in what is now the first row
    if 'Post title' in str(new_df.iloc[0, 0]):
        # Get the actual column names from the first row
        new_columns = new_df.iloc[0].tolist()
        new_df.columns = new_columns
        # Skip the first row (which is now our header)
        new_df = new_df.iloc[1:].reset_index(drop=True)
    
    # Rename columns in new_df to match our desired schema
    new_df = new_df.rename(columns={
        'Post title': 'text',
        'Post link': 'url',
        'Created date': 'created_date',
        'Posted by': 'author',
        'Impressions': 'impressions',
        'Views': 'views',
        'Clicks': 'clicks',
        'Click through rate (CTR)': 'ctr',
        'Likes': 'likes',
        'Comments': 'comments',
        'Reposts': 'shares',
        'Engagement rate': 'engagement_rate',
        'Content Type': 'content_type'
    })
    
    # Extract post IDs from URLs in new data
    new_df['post_id'] = new_df['url'].apply(extract_post_id_from_url)
    
    # Clean text content
    new_df['text'] = new_df['text'].apply(clean_text)
    original_df['text'] = original_df['text'].apply(clean_text)
    
    # Create a mapping from original data
    original_mapping = {}
    for _, row in original_df.iterrows():
        post_id = str(row['post_id']) if pd.notna(row['post_id']) else None
        if post_id:
            original_mapping[post_id] = {
                'url': row['url'],
                'uploaded_on': row['uploaded_on'],
                'utc_timestamp': row['utc_timestamp'],
                'hashtags': row['hashtags']
            }
    
    # Merge data
    merged_rows = []
    
    for _, new_row in new_df.iterrows():
        post_id = str(new_row['post_id']) if pd.notna(new_row['post_id']) else None
        
        # Create merged row
        merged_row = {
            'url': new_row['url'],
            'post_id': new_row['post_id'],
            'text': new_row['text'],
            'author': new_row['author'],
            'created_date': new_row['created_date'],
            'impressions': new_row.get('impressions', ''),
            'views': new_row.get('views', ''),
            'clicks': new_row.get('clicks', ''),
            'ctr': new_row.get('ctr', ''),
            'likes': new_row.get('likes', ''),
            'comments': new_row.get('comments', ''),
            'shares': new_row.get('shares', ''),
            'engagement_rate': new_row.get('engagement_rate', ''),
            'content_type': new_row.get('content_type', ''),
            'uploaded_on': '',
            'utc_timestamp': '',
            'hashtags': ''
        }
        
        # Add original data if available
        if post_id and post_id in original_mapping:
            original_data = original_mapping[post_id]
            merged_row.update({
                'uploaded_on': original_data['uploaded_on'],
                'utc_timestamp': original_data['utc_timestamp'],
                'hashtags': original_data['hashtags']
            })
        
        merged_rows.append(merged_row)
    
    # Create final dataframe
    merged_df = pd.DataFrame(merged_rows)
    
    # Reorder columns for better readability
    column_order = [
        'url', 'post_id', 'text', 'author', 'created_date', 'uploaded_on', 'utc_timestamp',
        'hashtags', 'impressions', 'views', 'clicks', 'ctr', 'likes', 'comments', 
        'shares', 'engagement_rate', 'content_type'
    ]
    
    # Only include columns that exist
    available_columns = [col for col in column_order if col in merged_df.columns]
    merged_df = merged_df[available_columns]
    
    # Save merged file
    output_file = 'linkedin_posts_merged.csv'
    merged_df.to_csv(output_file, index=False)
    
    print(f"\nMerge completed!")
    print(f"Original file: {len(original_df)} posts")
    print(f"New file: {len(new_df)} posts")
    print(f"Merged file: {len(merged_df)} posts")
    print(f"Output saved to: {output_file}")
    
    # Show sample of merged data
    print(f"\nSample of merged data:")
    print(merged_df[['text', 'impressions', 'likes', 'engagement_rate']].head(3))
    
    return merged_df

if __name__ == "__main__":
    merged_df = merge_linkedin_files()
