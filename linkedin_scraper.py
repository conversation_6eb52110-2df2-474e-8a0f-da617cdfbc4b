#!/usr/bin/env python3
"""
LinkedIn Post Scraper
Scrapes LinkedIn posts and extracts metadata including timestamps, text content, and other information.
"""

import re
import requests
import csv
import time
from urllib.parse import urlparse, parse_qs
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LinkedInPostScraper:
    def __init__(self):
        self.setup_driver()
        
    def setup_driver(self):
        """Set up Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def extract_post_id(self, url):
        """Extract post ID from LinkedIn URL"""
        try:
            # LinkedIn post URLs typically have format:
            # https://www.linkedin.com/posts/username_activity-POSTID-HASH
            pattern = r'activity-(\d+)-'
            match = re.search(pattern, url)
            if match:
                return match.group(1)
            else:
                logger.warning(f"Could not extract post ID from URL: {url}")
                return None
        except Exception as e:
            logger.error(f"Error extracting post ID from {url}: {e}")
            return None
    
    def get_timestamp_from_api(self, url):
        """Get timestamp using the external timestamp extraction service"""
        try:
            # The service appears to be a client-side tool, so we'll need to use Selenium
            timestamp_url = "https://ollie-boyd.github.io/Linkedin-post-timestamp-extractor/"
            
            self.driver.get(timestamp_url)
            time.sleep(2)
            
            # Find the input field and enter the URL
            url_input = self.driver.find_element(By.CSS_SELECTOR, "input[type='text']")
            url_input.clear()
            url_input.send_keys(url)
            
            # Click the "Get uploaded date" button
            get_date_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Get uploaded date')]")
            get_date_button.click()
            
            # Wait for results
            time.sleep(3)
            
            # Try to extract the timestamp information
            try:
                # Look for the uploaded date text
                uploaded_text = self.driver.find_element(By.XPATH, "//text()[contains(., 'Uploaded on:')]/following-sibling::*").text
                utc_text = self.driver.find_element(By.XPATH, "//text()[contains(., 'UTC:')]/following-sibling::*").text
                
                return {
                    'uploaded_on': uploaded_text.strip(),
                    'utc': utc_text.strip()
                }
            except:
                # If specific elements not found, try to get all text and parse
                page_text = self.driver.find_element(By.TAG_NAME, "body").text
                
                uploaded_match = re.search(r'Uploaded on:\s*(.+)', page_text)
                utc_match = re.search(r'UTC:\s*(.+)', page_text)
                
                return {
                    'uploaded_on': uploaded_match.group(1).strip() if uploaded_match else '',
                    'utc': utc_match.group(1).strip() if utc_match else ''
                }
                
        except Exception as e:
            logger.error(f"Error getting timestamp for {url}: {e}")
            return {'uploaded_on': '', 'utc': ''}
    
    def scrape_post_content(self, url):
        """Scrape the actual post content from LinkedIn"""
        try:
            self.driver.get(url)
            time.sleep(3)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            post_data = {
                'text': '',
                'author': '',
                'likes': '',
                'comments': '',
                'shares': '',
                'hashtags': []
            }
            
            # Try to extract post text
            try:
                # Multiple selectors to try for post content
                text_selectors = [
                    "[data-test-id='main-feed-activity-card'] .feed-shared-text",
                    ".feed-shared-text",
                    "[data-test-id='post-text']",
                    ".feed-shared-update-v2__description",
                    ".attributed-text-segment-list__content"
                ]
                
                for selector in text_selectors:
                    try:
                        text_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        post_data['text'] = text_element.text.strip()
                        break
                    except:
                        continue
                        
            except Exception as e:
                logger.warning(f"Could not extract post text: {e}")
            
            # Try to extract author name
            try:
                author_selectors = [
                    ".feed-shared-actor__name",
                    ".feed-shared-actor__title",
                    "[data-test-id='post-author-name']"
                ]
                
                for selector in author_selectors:
                    try:
                        author_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        post_data['author'] = author_element.text.strip()
                        break
                    except:
                        continue
                        
            except Exception as e:
                logger.warning(f"Could not extract author: {e}")
            
            # Extract hashtags from text
            if post_data['text']:
                hashtags = re.findall(r'#\w+', post_data['text'])
                post_data['hashtags'] = hashtags
            
            return post_data
            
        except Exception as e:
            logger.error(f"Error scraping post content from {url}: {e}")
            return {
                'text': '',
                'author': '',
                'likes': '',
                'comments': '',
                'shares': '',
                'hashtags': []
            }
    
    def scrape_post(self, url):
        """Scrape a single LinkedIn post and return all data"""
        logger.info(f"Scraping post: {url}")
        
        post_id = self.extract_post_id(url)
        timestamp_data = self.get_timestamp_from_api(url)
        content_data = self.scrape_post_content(url)
        
        return {
            'url': url,
            'post_id': post_id,
            'uploaded_on': timestamp_data.get('uploaded_on', ''),
            'utc_timestamp': timestamp_data.get('utc', ''),
            'text': content_data.get('text', ''),
            'author': content_data.get('author', ''),
            'hashtags': ', '.join(content_data.get('hashtags', [])),
            'likes': content_data.get('likes', ''),
            'comments': content_data.get('comments', ''),
            'shares': content_data.get('shares', '')
        }
    
    def scrape_multiple_posts(self, urls):
        """Scrape multiple LinkedIn posts"""
        results = []
        
        for i, url in enumerate(urls, 1):
            logger.info(f"Processing post {i}/{len(urls)}")
            
            try:
                post_data = self.scrape_post(url.strip())
                results.append(post_data)
                
                # Add delay between requests to be respectful
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error processing {url}: {e}")
                # Add empty record for failed scrapes
                results.append({
                    'url': url,
                    'post_id': '',
                    'uploaded_on': '',
                    'utc_timestamp': '',
                    'text': '',
                    'author': '',
                    'hashtags': '',
                    'likes': '',
                    'comments': '',
                    'shares': ''
                })
        
        return results
    
    def save_to_csv(self, data, filename='linkedin_posts.csv'):
        """Save scraped data to CSV file"""
        try:
            df = pd.DataFrame(data)
            df.to_csv(filename, index=False, encoding='utf-8')
            logger.info(f"Data saved to {filename}")
            return filename
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
    
    def close(self):
        """Close the WebDriver"""
        if hasattr(self, 'driver'):
            self.driver.quit()

def main():
    """Main function to run the scraper"""
    scraper = LinkedInPostScraper()
    
    try:
        # Read URLs from file
        with open('linkedin_urls.txt', 'r') as f:
            urls = [line.strip() for line in f if line.strip()]
        
        logger.info(f"Found {len(urls)} URLs to process")
        
        # Scrape all posts
        results = scraper.scrape_multiple_posts(urls)
        
        # Save to CSV
        filename = scraper.save_to_csv(results)
        
        if filename:
            print(f"\nScraping completed! Results saved to: {filename}")
            print(f"Processed {len(results)} posts")
            
            # Display summary
            df = pd.read_csv(filename)
            print(f"\nSummary:")
            print(f"- Total posts: {len(df)}")
            print(f"- Posts with text: {len(df[df['text'] != ''])}")
            print(f"- Posts with timestamps: {len(df[df['uploaded_on'] != ''])}")
        
    except FileNotFoundError:
        print("Error: linkedin_urls.txt file not found!")
    except Exception as e:
        logger.error(f"Error in main: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
